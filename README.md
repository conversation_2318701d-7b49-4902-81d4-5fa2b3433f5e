# Textbook Platform Demo

A Vue.js application for managing textbooks with QR code generation for items. This demo interface allows you to create books, chapters, and items, with automatic QR code generation for each item.

## Features

- ✅ **Book Management**: Add and view textbooks
- ✅ **Chapter Management**: Add chapters to books
- ✅ **Item Management**: Add different types of items (questions, text, images, links)
- ✅ **Smooth QR Codes**: Beautiful QR codes with rounded modules and embedded icons
- ✅ **Interactive Questions**: Questions allow user interaction without showing answers initially
- ✅ **Network Access**: Host on network for access from other devices
- ✅ **Desktop Preview**: Preview content pages without copying URLs
- ✅ **Responsive Design**: Optimized for both desktop and mobile viewing
- ✅ **Secure Access**: QR codes lead to secure, token-based display pages (24-hour expiry)
- ✅ **Smart Redirects**: Link items automatically redirect to their URLs
- ✅ **File Persistence**: Changes are saved to `public/books.json`
- ✅ **Download QR Codes**: Download QR codes as PNG files

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm

### Installation

1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application

#### Run Everything Together (Recommended)
```bash
npm run dev
```

This will start:
- Backend server on `http://localhost:3001` (network accessible)
- Frontend development server on `http://localhost:5173` (network accessible)
- Both services accessible from other devices on your network

**✅ Server Status**: Yes, the server runs on port 3001 and should be accessible at `http://localhost:3001/api/books`

#### Option 2: Run Separately

**Terminal 1 - Backend:**
```bash
npm run server
```

**Terminal 2 - Frontend:**
```bash
npm run frontend
```

### Network Access

The application is configured to be accessible from other devices on your network:
- **Frontend**: `http://[your-ip]:5173`
- **Backend**: `http://[your-ip]:3001`

To find your IP address:
- **Windows**: `ipconfig`
- **Mac/Linux**: `ifconfig` or `ip addr`

### Usage

1. **Adding Books**: Click the "+" button next to "Books" in the sidebar
2. **Adding Chapters**: Select a book, then click the "+" button next to "Chapters & Items"
3. **Adding Items**: Select a chapter, then fill out the item form on the right
4. **Viewing QR Codes**:
   - After creating an item, a smooth QR code with rounded modules and embedded icon will automatically appear
   - Click on existing items in the tree to view their QR codes
   - Choose different styling themes from the dropdown
   - **Preview Content**: Click "Preview Content" to see how the page will look without scanning
   - Download QR codes using the "Download QR Code" button
   - Scan QR codes to view beautifully formatted content pages
   - **Interactive Questions**: Questions allow users to select answers and get immediate feedback
   - Link items will redirect directly to their URLs when scanned

### QR Code Features

The application generates smooth, modern QR codes with:
- **Rounded Modules**: Smooth, rounded shapes instead of harsh squares (like WhatsApp/Telegram)
- **Embedded Icons**: Each item type gets its own icon (❓ for questions, 🔗 for links, 📄 for text, 🖼️ for images)
- **Multiple Styles**: Choose from Default (Gray), Blue, Green, Purple, or High Contrast themes
- **High Quality**: 400x400px resolution with error correction
- **Secure Access**: Each QR code contains a unique token that expires in 24 hours
- **Interactive Content**: Questions provide immediate feedback without showing answers initially
- **Desktop Preview**: Preview content without scanning the QR code
- **Beautiful Display**: Scanning leads to responsive, mobile-friendly content pages

### File Structure

```
├── src/
│   ├── components/
│   │   ├── common/
│   │   │   ├── ItemForm.vue          # Form for creating items
│   │   │   └── QRCodeDisplay.vue     # QR code generation and display
│   │   ├── layout/
│   │   │   └── Sidebar.vue           # Navigation sidebar
│   │   └── views/
│   │       └── BookDetails.vue       # Main content area
│   ├── stores/
│   │   └── main.ts                   # Pinia store for state management
│   └── types/                        # TypeScript type definitions
├── public/
│   └── books.json                    # Data persistence file
└── server.js                         # Express backend server
```

### API Endpoints

The backend provides the following endpoints:

- `GET /api/books` - Get all books
- `POST /api/books` - Add a new book
- `POST /api/books/:bookId/chapters` - Add a chapter to a book
- `POST /api/books/:bookId/chapters/:chapterId/items` - Add an item to a chapter
- `GET /api/qr/:bookId/:chapterId/:itemId` - Generate fancy QR code with icon
- `GET /api/qr-styled/:bookId/:chapterId/:itemId?style=<style>` - Generate styled fancy QR code
- `GET /display/:token` - Display item content (QR code access only)

### QR Code Access

Each QR code contains a secure URL that leads to a beautiful display page:
- **Security**: Each QR code contains a unique token that expires in 24 hours
- **Direct Access**: QR codes can only be accessed by scanning - no direct URL guessing
- **Smart Handling**:
  - Link items redirect directly to their target URL
  - Other items show a beautiful, responsive display page
- **Mobile Optimized**: Display pages work perfectly on both desktop and mobile devices

### Future Firebase Integration

This demo is designed to be easily migrated to Firebase:
- The store methods are already async and can be updated to use Firebase APIs
- The data structure is compatible with Firestore
- QR code generation can be moved to Firebase Functions

### Development

- Built with Vue 3 + TypeScript
- Uses Pinia for state management
- PrimeVue for UI components
- Tailwind CSS for styling
- Express.js for backend API
- QRCode library for QR generation

### Notes

- The application automatically falls back to local state if the backend is not available
- QR codes are generated server-side for better performance and customization
- All changes are persisted to the `books.json` file in real-time
