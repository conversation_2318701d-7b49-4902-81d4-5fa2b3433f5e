{"name": "textbook-platform", "private": true, "version": "0.1.0", "main": "electron-main.cjs", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"npm run frontend\"", "frontend": "vite --host 0.0.0.0", "server": "node server.cjs", "build": "vue-tsc -b && vite build", "preview": "vite preview", "electron:dev": "concurrently \"npm run frontend\" \"cross-env NODE_ENV=development electron .\"", "electron:build": "npm run build && electron-builder", "electron:serve": "concurrently \"npm run server\" \"npm run electron:dev\""}, "build": {"appId": "com.textbook.platform", "productName": "Textbook Platform", "files": ["dist/**/*", "electron-main.js", "preload.js"], "directories": {"output": "electron-dist"}, "mac": {"category": "public.app-category.education"}, "win": {"target": "nsis"}, "linux": {"target": ["AppImage", "deb"]}}, "dependencies": {"@primeuix/themes": "^1.1.2", "@tailwindcss/vite": "^4.1.10", "canvas": "^3.1.0", "cors": "^2.8.5", "express": "^5.1.0", "jsdom": "^26.1.0", "multer": "^2.0.1", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.5", "qr-code-styling": "^1.9.2", "sharp": "^0.34.2", "tailwindcss": "^4.1.10", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^37.0.0", "electron-builder": "^26.0.12", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}