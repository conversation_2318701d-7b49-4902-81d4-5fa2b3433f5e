import express from 'express';
import cors from 'cors';
import { promises as fs } from 'fs';
import path from 'path';
// Import QRCodeStyling for Node.js environment
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const { QRCodeStyling } = require('qr-code-styling/lib/qr-code-styling.common.js');
const nodeCanvas = require('canvas');
const { JSDOM } = require('jsdom');
import { fileURLToPath } from 'url';
import crypto from 'crypto';
import os from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const BOOKS_FILE = path.join(__dirname, 'public', 'books.json');

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Helper function to read books
async function readBooks() {
  try {
    const data = await fs.readFile(BOOKS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading books:', error);
    return [];
  }
}

// Helper function to write books
async function writeBooks(books) {
  try {
    await fs.writeFile(BOOKS_FILE, JSON.stringify(books, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing books:', error);
    return false;
  }
}

// Helper function to generate meaningful IDs
function generateMeaningfulId(type, books, bookId = null, chapterId = null) {
  switch (type) {
    case 'book':
      const bookCount = books.length + 1;
      return `book${bookCount}`;

    case 'chapter':
      const book = books.find(b => b.id === bookId);
      if (!book) return `ch${Date.now()}`;
      const chapterCount = book.chapters.length + 1;
      return `${bookId}-ch${chapterCount}`;

    case 'item':
      const targetBook = books.find(b => b.id === bookId);
      if (!targetBook) return `item${Date.now()}`;
      const targetChapter = targetBook.chapters.find(c => c.id === chapterId);
      if (!targetChapter) return `item${Date.now()}`;
      const itemCount = targetChapter.items.length + 1;
      return `${chapterId}-item${itemCount}`;

    default:
      return Date.now().toString();
  }
}

// Generate secure token for QR access
function generateSecureToken() {
  return crypto.randomBytes(32).toString('hex');
}

// Get the network IP address
function getNetworkIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost'; // fallback
}

// Store for QR tokens (in production, use Redis or database)
const qrTokens = new Map();

// Store for user sessions (in production, use Redis or database)
const userSessions = new Map();

// Default users (in production, use proper database with hashed passwords)
const defaultUsers = [
  {
    id: 'admin-1',
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123', // In production, this should be hashed
    role: 'admin',
    createdAt: Date.now()
  },
  {
    id: 'editor-1',
    username: 'editor',
    email: '<EMAIL>',
    password: 'editor123', // In production, this should be hashed
    role: 'editor',
    createdAt: Date.now()
  },
  {
    id: 'viewer-1',
    username: 'viewer',
    email: '<EMAIL>',
    password: 'viewer123', // In production, this should be hashed
    role: 'viewer',
    createdAt: Date.now()
  }
];

// Simple authentication middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  const session = userSessions.get(token);
  if (!session) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }

  req.user = session.user;
  next();
}

// Permission check middleware
function requirePermission(permission) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userPermissions = getRolePermissions(req.user.role);
    if (!userPermissions[permission]) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}

// Get role permissions
function getRolePermissions(role) {
  switch (role) {
    case 'viewer':
      return {
        canView: true,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'editor':
      return {
        canView: true,
        canEdit: true,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: true,
        canCreateBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    default:
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
  }
}

// Helper function to create smooth QR code with rounded modules
async function createSmoothQRCode(data, itemType, color = '#1f2937', backgroundColor = '#ffffff') {
  try {
    console.log(`Generating QR code for ${itemType} with color ${color} and background ${backgroundColor}`);
    const colors = getColorsFromHex(color, backgroundColor);

    const qrCodeOptions = {
      width: 800,  // Increased from 280 to 800 for better print quality
      height: 800, // Increased from 280 to 800 for better print quality
      margin: 40,  // Add padding around the QR code for better scanning
      data: data,
      dotsOptions: {
        color: colors.dark,
        type: "rounded"
      },
      backgroundOptions: {
        color: colors.light,
      },
      cornersSquareOptions: {
        color: colors.dark,
        type: "extra-rounded"
      },
      cornersDotOptions: {
        color: colors.dark,
        type: "dot"
      },
      imageOptions: {
        margin: 20  // Add padding/margin around the QR code for better scanning
      }
    };

    // Try to add icon if available
    const iconPath = path.join(__dirname, 'public', 'icons', `${itemType}.svg`);
    try {
      await fs.access(iconPath);
      console.log(`Using icon: ${iconPath}`);
      qrCodeOptions.image = iconPath;
      qrCodeOptions.imageOptions = {
        crossOrigin: "anonymous",
        margin: 20,
        imageSize: 0.3,
        hideBackgroundDots: true
      };
    } catch (error) {
      console.log(`Icon not found for type: ${itemType}, using default`);
    }

    console.log('Creating QRCodeStyling instance...');
    // Use the correct Node.js syntax from the documentation
    const qrCode = new QRCodeStyling({
      jsdom: JSDOM, // this is required
      nodeCanvas, // this is required
      ...qrCodeOptions,
      imageOptions: {
        saveAsBlob: true,
        crossOrigin: "anonymous",
        margin: 15,
        imageSize: 0.25,
        hideBackgroundDots: true,
        ...qrCodeOptions.imageOptions
      }
    });

    console.log('Getting raw data as buffer...');
    // Use getRawData with proper type specification
    const buffer = await qrCode.getRawData("png");

    if (!buffer) {
      throw new Error('Failed to generate QR code buffer');
    }

    console.log('QR code generated successfully, buffer size:', buffer.length);
    return buffer;

  } catch (error) {
    console.error('Error in createSmoothQRCode:', error);
    console.error('Error details:', error.message);
    throw new Error(`QR code generation failed: ${error.message}`);
  }
}

// Get colors from hex color
function getColorsFromHex(hexColor, backgroundColor) {
  // Validate hex color format
  if (!hexColor || !hexColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    hexColor = '#1f2937'; // Default gray
  }

  // Validate background color format
  if (!backgroundColor || !backgroundColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    backgroundColor = '#ffffff'; // Default white
  }

  return {
    dark: hexColor,
    light: backgroundColor
  };
}

// Authentication endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    // Find user (in production, use proper database lookup with hashed passwords)
    const user = defaultUsers.find(u => u.username === username && u.password === password);

    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate session token
    const token = crypto.randomBytes(32).toString('hex');
    const session = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt,
        lastLogin: Date.now()
      },
      createdAt: Date.now()
    };

    // Store session (expires in 24 hours)
    userSessions.set(token, session);
    setTimeout(() => userSessions.delete(token), 24 * 60 * 60 * 1000);

    res.json({
      user: session.user,
      token: token,
      permissions: getRolePermissions(user.role)
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/auth/logout', authenticateToken, async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      userSessions.delete(token);
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    res.json({
      user: req.user,
      permissions: getRolePermissions(req.user.role)
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all books (requires authentication)
app.get('/api/books', authenticateToken, requirePermission('canView'), async (req, res) => {
  const books = await readBooks();
  res.json(books);
});

// Add a new book (admin only)
app.post('/api/books', authenticateToken, requirePermission('canCreateBooks'), async (req, res) => {
  try {
    const books = await readBooks();
    const newBook = {
      id: generateMeaningfulId('book', books),
      title: req.body.title,
      description: req.body.description || '',
      chapters: []
    };

    books.push(newBook);
    const success = await writeBooks(books);

    if (success) {
      res.json(newBook);
    } else {
      res.status(500).json({ error: 'Failed to save book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add a chapter to a book (admin only)
app.post('/api/books/:bookId/chapters', authenticateToken, requirePermission('canCreateChapters'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const newChapter = {
      id: generateMeaningfulId('chapter', books, req.params.bookId),
      title: req.body.title,
      items: []
    };

    book.chapters.push(newChapter);
    const success = await writeBooks(books);

    if (success) {
      res.json(newChapter);
    } else {
      res.status(500).json({ error: 'Failed to save chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add an item to a chapter (editor or admin)
app.post('/api/books/:bookId/chapters/:chapterId/items', authenticateToken, requirePermission('canEdit'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const newItem = {
      id: generateMeaningfulId('item', books, req.params.bookId, req.params.chapterId),
      ...req.body
    };

    chapter.items.push(newItem);
    const success = await writeBooks(books);

    if (success) {
      res.json(newItem);
    } else {
      res.status(500).json({ error: 'Failed to save item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete a book
app.delete('/api/books/:bookId', async (req, res) => {
  try {
    const books = await readBooks();
    const bookIndex = books.findIndex(b => b.id === req.params.bookId);

    if (bookIndex === -1) {
      return res.status(404).json({ error: 'Book not found' });
    }

    books.splice(bookIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Book deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete a chapter
app.delete('/api/books/:bookId/chapters/:chapterId', async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapterIndex = book.chapters.findIndex(c => c.id === req.params.chapterId);
    if (chapterIndex === -1) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    book.chapters.splice(chapterIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Chapter deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update an item in a chapter (editor or admin)
app.put('/api/books/:bookId/chapters/:chapterId/items/:itemId', authenticateToken, requirePermission('canEdit'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const itemIndex = chapter.items.findIndex(i => i.id === req.params.itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Update the item while preserving the ID
    const updatedItem = {
      ...chapter.items[itemIndex],
      ...req.body,
      id: req.params.itemId // Ensure ID never changes
    };

    chapter.items[itemIndex] = updatedItem;
    const success = await writeBooks(books);

    if (success) {
      res.json(updatedItem);
    } else {
      res.status(500).json({ error: 'Failed to update item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete an item (admin only)
app.delete('/api/books/:bookId/chapters/:chapterId/items/:itemId', authenticateToken, requirePermission('canDelete'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const itemIndex = chapter.items.findIndex(i => i.id === req.params.itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }

    chapter.items.splice(itemIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Item deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate QR code for an item
app.get('/api/qr/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create permanent URL using item IDs (no expiring tokens)
    const networkIP = getNetworkIP();
    const displayUrl = `${req.protocol}://${networkIP}:${PORT}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with default gray color and white background
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, '#1f2937', '#ffffff');
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate stylized QR code with custom colors
app.get('/api/qr-styled/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const color = req.query.color || '#1f2937';
    const backgroundColor = req.query.backgroundColor || '#ffffff';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }







    // Create permanent URL using item IDs (no expiring tokens)
    const networkIP = getNetworkIP();
    const displayUrl = `${req.protocol}://${networkIP}:${PORT}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with selected colors
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, color, backgroundColor);
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      styled: true,
      color: color,
      backgroundColor: backgroundColor,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Permanent display page for QR code access using item IDs
app.get('/item/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📚 Book Not Found</h1>
            <p>The requested book could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Chapter Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📖 Chapter Not Found</h1>
            <p>The requested chapter could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📄 Item Not Found</h1>
            <p>The requested item could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, book.title, chapter.title);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Legacy display page for backward compatibility (will show deprecation notice)
app.get('/display/:token', async (req, res) => {
  try {
    const { token } = req.params;
    const tokenData = qrTokens.get(token);

    if (!tokenData) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Invalid or Expired Link</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🔒 Access Denied</h1>
            <p>This link is invalid or has expired. Please generate a new QR code.</p>
            <p><small>Note: QR codes now use permanent links that never expire.</small></p>
          </div>
        </body>
        </html>
      `);
    }

    const { item, bookTitle, chapterTitle } = tokenData;

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, bookTitle, chapterTitle);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Generate beautiful display page
function generateDisplayPage(item, bookTitle, chapterTitle) {
  const getItemIcon = (type) => {
    const icons = {
      question: '❓',
      test: '📝',
      text: '📄',
      image: '🖼️',
      link: '🔗',
      map: '🗺️',
      diagram: '📊',
      'timed-question': '⏰'
    };
    return icons[type] || '📋';
  };

  const renderItemContent = (item) => {
    switch (item.type) {
      case 'question':
        return `
          <div class="question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            ${item.options ? `
              <div class="options-container" id="optionsContainer">
                ${item.options.map((option, index) => `
                  <div class="option" data-index="${index}" onclick="selectOption(${index}, ${item.correctAnswer})">
                    <span class="option-letter">${String.fromCharCode(65 + index)}</span>
                    <span class="option-text">${option.text}</span>
                  </div>
                `).join('')}
              </div>
              <div id="result" class="result-container" style="display: none;">
                <div id="resultMessage" class="result-message"></div>
                <button onclick="resetQuestion()" class="reset-button">Try Again</button>
              </div>
            ` : ''}
          </div>
        `;
      case 'test':
        return `
          <div class="test-container">
            <div class="test-header">
              <h2 class="test-title">📝 ${item.title}</h2>
              <div class="test-progress">
                <span id="currentQuestion">1</span> of <span id="totalQuestions">${item.testQuestions?.length || 0}</span>
              </div>
            </div>

            <div class="test-content">
              ${item.testQuestions ? item.testQuestions.map((question, qIndex) => `
                <div class="test-question ${qIndex === 0 ? 'active' : 'hidden'}" data-question="${qIndex}">
                  <h3 class="question-text">${question.question}</h3>
                  <div class="test-options">
                    ${question.options.map((option, oIndex) => `
                      <div class="test-option" data-question="${qIndex}" data-option="${oIndex}" onclick="selectTestOption(${qIndex}, ${oIndex}, ${question.correctAnswer})">
                        <span class="option-letter">${String.fromCharCode(65 + oIndex)}</span>
                        <span class="option-text">${option.text}</span>
                      </div>
                    `).join('')}
                  </div>
                </div>
              `).join('') : '<p>No questions available</p>'}
            </div>

            <div class="test-navigation">
              <button id="prevBtn" onclick="previousQuestion()" disabled>Previous</button>
              <button id="nextBtn" onclick="nextQuestion()">Next</button>
              <button id="finishBtn" onclick="finishTest()" style="display: none;">Finish Test</button>
            </div>

            <div id="testResults" class="test-results" style="display: none;">
              <div class="results-header">
                <h3>Test Complete!</h3>
                <div class="score-circle">
                  <div class="score-text">
                    <span id="scoreNumber">0</span>/<span id="totalScore">${item.testQuestions?.length || 0}</span>
                  </div>
                </div>
              </div>

              <div class="results-list">
                <h4>Question Review:</h4>
                <div id="questionReview"></div>
              </div>

              <div class="results-actions">
                <button onclick="retakeTest()" class="retry-button">Retake Test</button>
                <button onclick="promptForNameAndDownload()" class="download-button">Download Results</button>
              </div>
            </div>
          </div>
        `;
      case 'text':
        return `
          <div class="text-container">
            <div class="text-content">${item.content || 'No content available'}</div>
          </div>
        `;
      case 'image':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🖼️</span>
              <p>Image: ${item.url || 'No image URL provided'}</p>
            </div>
          </div>
        `;
      case 'map':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🗺️</span>
              <p>Map: ${item.title}</p>
              <small>Map image would be displayed here</small>
            </div>
          </div>
        `;
      case 'diagram':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">📊</span>
              <p>Diagram: ${item.title}</p>
              <small>Diagram image would be displayed here</small>
            </div>
          </div>
        `;
      case 'timed-question':
        return `
          <div class="timed-question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            <div class="timer-container">
              <div class="timer-display" id="timerDisplay">
                <span class="timer-icon">⏰</span>
                <span class="timer-text" id="timerText">${item.revealTimeSeconds || 30}</span>
                <span class="timer-label">seconds</span>
              </div>
            </div>
            <div class="answer-container" id="answerContainer" style="display: none;">
              <h3 class="answer-title">Answer:</h3>
              <div class="answer-content">${item.timedAnswer || 'No answer provided'}</div>
            </div>
            <button id="startTimerBtn" class="start-timer-button" onclick="startTimer()">Start Timer</button>
            <button id="resetTimerBtn" class="reset-timer-button" onclick="resetTimer()" style="display: none;">Reset</button>
          </div>
        `;
      default:
        return `<div class="default-content">Content not available</div>`;
    }
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${item.title} - ${bookTitle}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          padding: 20px;
          line-height: 1.6;
        }

        .container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          overflow: hidden;
          animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .header {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          text-align: center;
        }

        .item-icon {
          font-size: 3rem;
          margin-bottom: 15px;
          display: block;
        }

        .item-title {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 10px;
        }

        .breadcrumb {
          opacity: 0.9;
          font-size: 0.9rem;
        }

        .content {
          padding: 40px;
        }

        .question-container {
          text-align: center;
        }

        .question-title {
          font-size: 1.5rem;
          color: #1a202c;
          margin-bottom: 30px;
          font-weight: 600;
        }

        .options-container {
          display: grid;
          gap: 15px;
          max-width: 600px;
          margin: 0 auto;
        }

        .option {
          display: flex;
          align-items: center;
          padding: 20px;
          background: #f7fafc;
          border-radius: 12px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          position: relative;
          cursor: pointer;
        }

        .option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .option.selected {
          background: #ebf8ff;
          border-color: #4299e1;
        }

        .option.correct {
          background: #f0fff4;
          border-color: #68d391;
          box-shadow: 0 4px 12px rgba(104, 211, 145, 0.2);
        }

        .option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
          box-shadow: 0 4px 12px rgba(252, 129, 129, 0.2);
        }

        .option.disabled {
          cursor: not-allowed;
          opacity: 0.7;
        }

        .option-letter {
          background: #4f46e5;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .option.correct .option-letter {
          background: #38a169;
        }

        .option.incorrect .option-letter {
          background: #e53e3e;
        }

        .option-text {
          flex: 1;
          font-size: 1.1rem;
        }

        .correct-indicator {
          color: #38a169;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .incorrect-indicator {
          color: #e53e3e;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .result-container {
          margin-top: 30px;
          text-align: center;
          padding: 20px;
          border-radius: 12px;
          animation: fadeIn 0.5s ease-out;
        }

        .result-message {
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 20px;
        }

        .result-message.correct {
          color: #38a169;
        }

        .result-message.incorrect {
          color: #e53e3e;
        }

        .reset-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .reset-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .text-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .text-content {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #2d3748;
          background: #f7fafc;
          padding: 30px;
          border-radius: 12px;
          border-left: 4px solid #4f46e5;
        }

        .image-container {
          text-align: center;
        }

        .image-placeholder {
          background: #f7fafc;
          border: 2px dashed #cbd5e0;
          border-radius: 12px;
          padding: 60px 30px;
          color: #718096;
        }

        .image-icon {
          font-size: 4rem;
          display: block;
          margin-bottom: 20px;
        }

        .timed-question-container {
          text-align: center;
          max-width: 600px;
          margin: 0 auto;
        }

        .timer-container {
          margin: 30px 0;
        }

        .timer-display {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          border-radius: 20px;
          display: inline-flex;
          align-items: center;
          gap: 15px;
          font-size: 2rem;
          font-weight: bold;
          box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        .timer-icon {
          font-size: 2.5rem;
        }

        .timer-text {
          font-size: 3rem;
          min-width: 80px;
        }

        .timer-label {
          font-size: 1.2rem;
          opacity: 0.9;
        }

        .answer-container {
          background: #f0fff4;
          border: 2px solid #68d391;
          border-radius: 12px;
          padding: 30px;
          margin: 30px 0;
          animation: slideDown 0.5s ease-out;
        }

        .answer-title {
          color: #38a169;
          font-size: 1.5rem;
          margin-bottom: 15px;
          font-weight: 600;
        }

        .answer-content {
          font-size: 1.2rem;
          line-height: 1.6;
          color: #2d3748;
          background: white;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #38a169;
        }

        @keyframes slideDown {
          from { opacity: 0; transform: translateY(-20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .start-timer-button, .reset-timer-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 10px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          margin: 10px;
        }

        .start-timer-button:hover, .reset-timer-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .reset-timer-button {
          background: #6b7280;
        }

        .reset-timer-button:hover {
          background: #4b5563;
        }

        .footer {
          background: #f7fafc;
          padding: 20px 40px;
          text-align: center;
          color: #718096;
          font-size: 0.9rem;
          border-top: 1px solid #e2e8f0;
        }

        /* Test Styles */
        .test-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .test-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 2px solid #e2e8f0;
        }

        .test-title {
          font-size: 1.8rem;
          color: #1a202c;
          font-weight: 600;
        }

        .test-progress {
          background: #4f46e5;
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-weight: 600;
        }

        .test-content {
          margin-bottom: 30px;
        }

        .test-question {
          animation: fadeIn 0.3s ease-out;
        }

        .test-question.hidden {
          display: none;
        }

        .question-text {
          font-size: 1.4rem;
          color: #2d3748;
          margin-bottom: 25px;
          font-weight: 600;
          line-height: 1.5;
        }

        .test-options {
          display: grid;
          gap: 12px;
        }

        .test-option {
          display: flex;
          align-items: center;
          padding: 16px;
          background: #f7fafc;
          border-radius: 10px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .test-option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-1px);
        }

        .test-option.selected {
          background: #ebf8ff;
          border-color: #4299e1;
        }

        .test-option.correct {
          background: #f0fff4;
          border-color: #68d391;
        }

        .test-option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
        }

        .test-navigation {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 30px;
        }

        .test-navigation button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .test-navigation button:hover:not(:disabled) {
          background: #4338ca;
          transform: translateY(-2px);
        }

        .test-navigation button:disabled {
          background: #9ca3af;
          cursor: not-allowed;
          transform: none;
        }

        .test-results {
          text-align: center;
          animation: fadeIn 0.5s ease-out;
        }

        .results-header {
          margin-bottom: 30px;
        }

        .results-header h3 {
          font-size: 2rem;
          color: #1a202c;
          margin-bottom: 20px;
        }

        .score-circle {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: conic-gradient(#4f46e5 0deg, #4f46e5 var(--score-angle, 0deg), #e2e8f0 var(--score-angle, 0deg));
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;
          position: relative;
        }

        .score-circle::before {
          content: '';
          width: 80px;
          height: 80px;
          background: white;
          border-radius: 50%;
          position: absolute;
        }

        .score-text {
          position: relative;
          z-index: 1;
          font-size: 1.5rem;
          font-weight: bold;
          color: #1a202c;
        }

        .results-list {
          text-align: left;
          margin-bottom: 30px;
        }

        .results-list h4 {
          font-size: 1.3rem;
          margin-bottom: 15px;
          color: #2d3748;
        }

        .question-result {
          display: flex;
          align-items: center;
          padding: 12px;
          margin-bottom: 8px;
          border-radius: 8px;
          background: #f7fafc;
        }

        .question-result.correct {
          background: #f0fff4;
          color: #38a169;
        }

        .question-result.incorrect {
          background: #fed7d7;
          color: #e53e3e;
        }

        .results-actions {
          display: flex;
          gap: 15px;
          justify-content: center;
        }

        .retry-button, .download-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .download-button {
          background: #059669;
        }

        .retry-button:hover {
          background: #4338ca;
        }

        .download-button:hover {
          background: #047857;
        }

        @media (max-width: 768px) {
          body { padding: 10px; }
          .container { border-radius: 15px; }
          .header { padding: 20px; }
          .item-title { font-size: 1.5rem; }
          .content { padding: 20px; }
          .question-title { font-size: 1.3rem; }
          .option { padding: 15px; }
          .option-text { font-size: 1rem; }
          .text-content { font-size: 1.1rem; padding: 20px; }
          .test-header { flex-direction: column; gap: 15px; text-align: center; }
          .test-title { font-size: 1.5rem; }
          .question-text { font-size: 1.2rem; }
          .results-actions { flex-direction: column; }
        }
      </style>
      <script>
        function selectOption(selectedIndex, correctAnswer) {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');
          const resultMessage = document.getElementById('resultMessage');

          // Disable all options
          options.forEach(option => {
            option.classList.add('disabled');
            option.onclick = null;
          });

          // Mark selected option
          options[selectedIndex].classList.add('selected');

          // Show correct/incorrect styling
          if (selectedIndex === correctAnswer) {
            options[selectedIndex].classList.add('correct');
            options[selectedIndex].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '🎉 Correct! Well done!';
            resultMessage.className = 'result-message correct';
          } else {
            options[selectedIndex].classList.add('incorrect');
            options[selectedIndex].innerHTML += '<span class="incorrect-indicator">✗</span>';
            options[correctAnswer].classList.add('correct');
            options[correctAnswer].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '❌ Incorrect. The correct answer is highlighted.';
            resultMessage.className = 'result-message incorrect';
          }

          // Show result
          resultContainer.style.display = 'block';
        }

        function resetQuestion() {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');

          // Reset all options
          options.forEach((option, index) => {
            option.className = 'option';
            option.onclick = () => selectOption(index, ${item.correctAnswer || 0});
            // Remove indicators
            const indicators = option.querySelectorAll('.correct-indicator, .incorrect-indicator');
            indicators.forEach(indicator => indicator.remove());
          });

          // Hide result
          resultContainer.style.display = 'none';
        }

        // Timed question functionality
        let timerInterval;
        let timeRemaining = ${item.revealTimeSeconds || 30};

        function startTimer() {
          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          startBtn.style.display = 'none';
          resetBtn.style.display = 'inline-block';

          timerInterval = setInterval(() => {
            timeRemaining--;
            timerText.textContent = timeRemaining;

            if (timeRemaining <= 0) {
              clearInterval(timerInterval);
              answerContainer.style.display = 'block';
              timerText.textContent = '0';
              resetBtn.textContent = 'Try Again';
            }
          }, 1000);
        }

        function resetTimer() {
          clearInterval(timerInterval);
          timeRemaining = ${item.revealTimeSeconds || 30};

          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          timerText.textContent = timeRemaining;
          answerContainer.style.display = 'none';
          startBtn.style.display = 'inline-block';
          resetBtn.style.display = 'none';
          resetBtn.textContent = 'Reset';
        }

        // Test functionality
        let currentQuestionIndex = 0;
        let testAnswers = [];
        let testQuestions = ${JSON.stringify(item.testQuestions || [])};

        function selectTestOption(questionIndex, optionIndex, correctAnswer) {
          const questionDiv = document.querySelector(\`[data-question="\${questionIndex}"]\`);
          const options = questionDiv.querySelectorAll('.test-option');

          // Clear previous selections for this question
          options.forEach(opt => {
            opt.classList.remove('selected');
          });

          // Mark selected option
          options[optionIndex].classList.add('selected');

          // Store answer
          testAnswers[questionIndex] = optionIndex;

          // Update navigation buttons
          updateNavigationButtons();
        }

        function nextQuestion() {
          if (currentQuestionIndex < testQuestions.length - 1) {
            // Hide current question
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.add('hidden');

            // Show next question
            currentQuestionIndex++;
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.remove('hidden');

            // Update progress
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;

            updateNavigationButtons();
          }
        }

        function previousQuestion() {
          if (currentQuestionIndex > 0) {
            // Hide current question
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.add('hidden');

            // Show previous question
            currentQuestionIndex--;
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.remove('hidden');

            // Update progress
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;

            updateNavigationButtons();
          }
        }

        function updateNavigationButtons() {
          const prevBtn = document.getElementById('prevBtn');
          const nextBtn = document.getElementById('nextBtn');
          const finishBtn = document.getElementById('finishBtn');

          // Update previous button
          prevBtn.disabled = currentQuestionIndex === 0;

          // Update next/finish buttons
          if (currentQuestionIndex === testQuestions.length - 1) {
            nextBtn.style.display = 'none';
            finishBtn.style.display = 'inline-block';
          } else {
            nextBtn.style.display = 'inline-block';
            finishBtn.style.display = 'none';
          }
        }

        function finishTest() {
          // Calculate score
          let correctCount = 0;
          testQuestions.forEach((question, index) => {
            if (testAnswers[index] === question.correctAnswer) {
              correctCount++;
            }
          });

          // Hide test content
          document.querySelector('.test-content').style.display = 'none';
          document.querySelector('.test-navigation').style.display = 'none';

          // Show results
          const resultsDiv = document.getElementById('testResults');
          resultsDiv.style.display = 'block';

          // Update score display
          document.getElementById('scoreNumber').textContent = correctCount;
          document.getElementById('totalScore').textContent = testQuestions.length;

          // Update score circle
          const scorePercentage = (correctCount / testQuestions.length) * 100;
          const scoreAngle = (scorePercentage / 100) * 360;
          document.querySelector('.score-circle').style.setProperty('--score-angle', \`\${scoreAngle}deg\`);

          // Generate question review
          const reviewDiv = document.getElementById('questionReview');
          reviewDiv.innerHTML = testQuestions.map((question, index) => {
            const isCorrect = testAnswers[index] === question.correctAnswer;
            const userAnswer = testAnswers[index] !== undefined ? question.options[testAnswers[index]]?.text : 'Not answered';
            const correctAnswer = question.options[question.correctAnswer]?.text;

            return \`
              <div class="question-result \${isCorrect ? 'correct' : 'incorrect'}">
                <span style="margin-right: 10px;">\${isCorrect ? '✓' : '✗'}</span>
                <div>
                  <strong>Q\${index + 1}:</strong> \${question.question}<br>
                  <small>Your answer: \${userAnswer}</small>
                  \${!isCorrect ? \`<br><small>Correct answer: \${correctAnswer}</small>\` : ''}
                </div>
              </div>
            \`;
          }).join('');
        }

        function retakeTest() {
          // Reset test state
          currentQuestionIndex = 0;
          testAnswers = [];

          // Hide results
          document.getElementById('testResults').style.display = 'none';

          // Show test content
          document.querySelector('.test-content').style.display = 'block';
          document.querySelector('.test-navigation').style.display = 'flex';

          // Reset all questions
          document.querySelectorAll('.test-question').forEach((q, index) => {
            q.classList.toggle('hidden', index !== 0);
            q.querySelectorAll('.test-option').forEach(opt => {
              opt.classList.remove('selected');
            });
          });

          // Reset progress
          document.getElementById('currentQuestion').textContent = '1';

          // Reset navigation
          updateNavigationButtons();
        }

        function promptForNameAndDownload() {
          const userName = prompt('Please enter your name for the test results:');
          if (userName && userName.trim() !== '') {
            downloadResults(userName.trim());
          }
        }

        function downloadResults(userName = 'Student') {
          // Create a canvas for the results image
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          // Add roundRect polyfill if not available
          if (!ctx.roundRect) {
            ctx.roundRect = function(x, y, width, height, radii) {
              if (typeof radii === 'number') {
                radii = [radii, radii, radii, radii];
              } else if (radii.length === 2) {
                radii = [radii[0], radii[1], radii[0], radii[1]];
              } else if (!radii || radii.length === 0) {
                radii = [0, 0, 0, 0];
              }

              this.beginPath();
              this.moveTo(x + radii[0], y);
              this.lineTo(x + width - radii[1], y);
              this.quadraticCurveTo(x + width, y, x + width, y + radii[1]);
              this.lineTo(x + width, y + height - radii[2]);
              this.quadraticCurveTo(x + width, y + height, x + width - radii[2], y + height);
              this.lineTo(x + radii[3], y + height);
              this.quadraticCurveTo(x, y + height, x, y + height - radii[3]);
              this.lineTo(x, y + radii[0]);
              this.quadraticCurveTo(x, y, x + radii[0], y);
              this.closePath();
            };
          }

          // Helper function to draw text with shadow
          function drawTextWithShadow(text, x, y, shadowColor = 'rgba(0,0,0,0.1)', shadowOffset = 2) {
            ctx.save();
            ctx.fillStyle = shadowColor;
            ctx.fillText(text, x + shadowOffset, y + shadowOffset);
            ctx.restore();
            ctx.fillText(text, x, y);
          }

          // Helper function to draw gradient text
          function drawGradientText(text, x, y, color1, color2) {
            const gradient = ctx.createLinearGradient(x - 100, y - 20, x + 100, y + 20);
            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);
            ctx.fillStyle = gradient;
            ctx.fillText(text, x, y);
          }

          // Set canvas size for better quality (Instagram post size)
          canvas.width = 1080;
          canvas.height = 1350;

          // Clear canvas to prevent glitches
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Modern gradient background
          const bgGradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
          bgGradient.addColorStop(0, '#667eea');
          bgGradient.addColorStop(0.5, '#764ba2');
          bgGradient.addColorStop(1, '#f093fb');
          ctx.fillStyle = bgGradient;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Decorative elements
          ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
          ctx.beginPath();
          ctx.arc(100, 100, 80, 0, 2 * Math.PI);
          ctx.fill();
          ctx.beginPath();
          ctx.arc(canvas.width - 100, canvas.height - 100, 60, 0, 2 * Math.PI);
          ctx.fill();

          // Main card background with shadow
          ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
          ctx.roundRect(35, 35, canvas.width - 70, canvas.height - 70, 25);
          ctx.fill();

          ctx.fillStyle = '#ffffff';
          ctx.roundRect(30, 30, canvas.width - 60, canvas.height - 60, 25);
          ctx.fill();

          // Header section with modern gradient
          const headerGradient = ctx.createLinearGradient(0, 30, 0, 250);
          headerGradient.addColorStop(0, '#4f46e5');
          headerGradient.addColorStop(1, '#7c3aed');
          ctx.fillStyle = headerGradient;
          ctx.roundRect(30, 30, canvas.width - 60, 220, [25, 25, 0, 0]);
          ctx.fill();

          // Decorative header pattern
          ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
          for (let i = 0; i < 5; i++) {
            ctx.beginPath();
            ctx.arc(100 + i * 200, 80, 30, 0, 2 * Math.PI);
            ctx.fill();
          }

          // Trophy/Achievement icon
          ctx.fillStyle = '#ffd700';
          ctx.font = 'bold 80px Arial';
          ctx.textAlign = 'center';
          ctx.fillText('🏆', canvas.width / 2, 130);

          // Main title with shadow
          ctx.fillStyle = '#ffffff';
          ctx.font = 'bold 42px Arial';
          drawTextWithShadow('Test Complete!', canvas.width / 2, 180, 'rgba(0,0,0,0.3)', 3);

          // Test details with elegant styling
          ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
          ctx.font = '22px Arial';
          ctx.fillText('${bookTitle} → ${chapterTitle}', canvas.width / 2, 210);

          ctx.font = 'bold 28px Arial';
          ctx.fillStyle = '#ffffff';
          ctx.fillText('${item.title}', canvas.width / 2, 240);

          // Student name with elegant styling
          ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
          ctx.font = '20px Arial';
          ctx.fillText(\`Student: \${userName}\`, canvas.width / 2, 270);

          // Score section with modern design
          const score = document.getElementById('scoreNumber').textContent;
          const total = document.getElementById('totalScore').textContent;
          const percentage = Math.round((score / total) * 100);
          const centerX = canvas.width / 2;
          const centerY = 420;

          // Score card background with shadow
          ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
          ctx.roundRect(centerX - 180, centerY - 120, 360, 200, 20);
          ctx.fill();

          // Score circle outer shadow
          ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
          ctx.beginPath();
          ctx.arc(centerX + 2, centerY + 2, 85, 0, 2 * Math.PI);
          ctx.fill();

          // Score circle background (white)
          ctx.fillStyle = '#ffffff';
          ctx.beginPath();
          ctx.arc(centerX, centerY, 85, 0, 2 * Math.PI);
          ctx.fill();

          // Score circle background track
          ctx.strokeStyle = '#f1f5f9';
          ctx.lineWidth = 16;
          ctx.beginPath();
          ctx.arc(centerX, centerY, 70, 0, 2 * Math.PI);
          ctx.stroke();

          // Score circle progress with gradient
          const scoreAngle = (percentage / 100) * 2 * Math.PI;
          const progressGradient = ctx.createLinearGradient(centerX - 70, centerY - 70, centerX + 70, centerY + 70);
          progressGradient.addColorStop(0, '#4f46e5');
          progressGradient.addColorStop(0.5, '#7c3aed');
          progressGradient.addColorStop(1, '#ec4899');

          ctx.strokeStyle = progressGradient;
          ctx.lineWidth = 16;
          ctx.lineCap = 'round';
          ctx.beginPath();
          ctx.arc(centerX, centerY, 70, -Math.PI / 2, -Math.PI / 2 + scoreAngle);
          ctx.stroke();

          // Score text with proper layering
          ctx.fillStyle = '#1a202c';
          ctx.font = 'bold 36px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(score + '/' + total, centerX, centerY + 5);

          // Percentage with gradient
          drawGradientText(percentage + '%', centerX, centerY + 40, '#4f46e5', '#7c3aed');
          ctx.font = 'bold 24px Arial';

          // Performance badge
          const performanceText = percentage >= 90 ? 'Excellent!' : percentage >= 80 ? 'Great Job!' : percentage >= 70 ? 'Good Work!' : percentage >= 60 ? 'Keep Trying!' : 'Study More!';
          const badgeColor = percentage >= 80 ? '#10b981' : percentage >= 60 ? '#f59e0b' : '#ef4444';

          ctx.fillStyle = badgeColor;
          ctx.roundRect(centerX - 60, centerY + 70, 120, 35, 17);
          ctx.fill();

          ctx.fillStyle = '#ffffff';
          ctx.font = 'bold 16px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(performanceText, centerX, centerY + 92);

          // Question Review section with modern styling
          ctx.fillStyle = '#1e293b';
          ctx.font = 'bold 32px Arial';
          ctx.textAlign = 'left';
          drawTextWithShadow('Question Review', 70, 580, 'rgba(0,0,0,0.1)', 2);

          // Questions list with better spacing
          let yPos = 630;
          const maxQuestionsToShow = Math.min(testQuestions.length, 6); // Reduced for better fit

          testQuestions.slice(0, maxQuestionsToShow).forEach((question, index) => {
            const isCorrect = testAnswers[index] === question.correctAnswer;
            const userAnswer = testAnswers[index] !== undefined ? question.options[testAnswers[index]]?.text || 'Not answered' : 'Not answered';
            const correctAnswer = question.options[question.correctAnswer]?.text || 'No answer';

            // Modern card design with shadow
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.roundRect(72, yPos - 32, canvas.width - 144, 100, 15);
            ctx.fill();

            // Question card background
            ctx.fillStyle = isCorrect ? '#ecfdf5' : '#fef2f2';
            ctx.roundRect(70, yPos - 30, canvas.width - 140, 95, 15);
            ctx.fill();

            // Left accent bar
            ctx.fillStyle = isCorrect ? '#10b981' : '#ef4444';
            ctx.roundRect(70, yPos - 30, 6, 95, [15, 0, 0, 15]);
            ctx.fill();

            // Status icon with circle background
            const iconX = 100;
            const iconY = yPos + 10;

            ctx.fillStyle = isCorrect ? '#10b981' : '#ef4444';
            ctx.beginPath();
            ctx.arc(iconX, iconY, 18, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(isCorrect ? '✓' : '✗', iconX, iconY + 7);

            // Question number and text with better typography
            ctx.fillStyle = '#1e293b';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'left';
            const questionText = \`Q\${index + 1}: \${question.question || 'No question text'}\`;
            const truncatedQuestion = questionText.length > 50 ? questionText.substring(0, 50) + '...' : questionText;
            ctx.fillText(truncatedQuestion, 130, yPos - 5);

            // User answer with proper styling
            ctx.fillStyle = '#64748b';
            ctx.font = '15px Arial';
            ctx.textAlign = 'left';
            const userAnswerText = \`Your answer: \${userAnswer.length > 40 ? userAnswer.substring(0, 40) + '...' : userAnswer}\`;
            ctx.fillText(userAnswerText, 130, yPos + 20);

            // Correct answer (if wrong) with better visibility
            if (!isCorrect) {
              ctx.fillStyle = '#059669';
              ctx.font = 'bold 15px Arial';
              const correctAnswerText = \`Correct: \${correctAnswer.length > 40 ? correctAnswer.substring(0, 40) + '...' : correctAnswer}\`;
              ctx.fillText(correctAnswerText, 130, yPos + 40);
            }

            yPos += 110; // Reduced spacing for better fit
          });

          // Show "and X more questions" if there are more
          if (testQuestions.length > maxQuestionsToShow) {
            ctx.fillStyle = '#64748b';
            ctx.font = 'italic 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(\`... and \${testQuestions.length - maxQuestionsToShow} more question(s)\`, canvas.width / 2, yPos + 30);
          }

          // Modern footer with gradient background
          const footerGradient = ctx.createLinearGradient(0, canvas.height - 80, 0, canvas.height);
          footerGradient.addColorStop(0, 'rgba(248, 250, 252, 0)');
          footerGradient.addColorStop(1, 'rgba(248, 250, 252, 1)');
          ctx.fillStyle = footerGradient;
          ctx.fillRect(30, canvas.height - 80, canvas.width - 60, 50);

          // Footer text with elegant styling
          ctx.fillStyle = '#64748b';
          ctx.font = '18px Arial';
          ctx.textAlign = 'center';
          const currentDate = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          ctx.fillText(\`📚 \${userName} • \${currentDate}\`, canvas.width / 2, canvas.height - 40);

          // Download with descriptive filename
          const link = document.createElement('a');
          const safeTestName = '${item.title}'.replace(/[^a-z0-9]/gi, '_').toLowerCase();
          const safeUserName = userName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
          link.download = \`\${safeUserName}_\${safeTestName}_results.png\`;
          link.href = canvas.toDataURL('image/png', 1.0);
          link.click();
        }
      </script>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <span class="item-icon">${getItemIcon(item.type)}</span>
          <h1 class="item-title">${item.title}</h1>
          <div class="breadcrumb">${bookTitle} → ${chapterTitle}</div>
        </div>

        <div class="content">
          ${renderItemContent(item)}
        </div>

        <div class="footer">
          <p>📚 Accessed via QR Code • ${new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

console.log('Starting server...');

app.listen(PORT, '0.0.0.0', () => {
  const networkIP = getNetworkIP();
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`🌐 Network access: http://${networkIP}:${PORT}`);
  console.log(`📚 API available at: http://${networkIP}:${PORT}/api/books`);
  console.log(`📱 QR codes will use: http://${networkIP}:${PORT}`);
});
