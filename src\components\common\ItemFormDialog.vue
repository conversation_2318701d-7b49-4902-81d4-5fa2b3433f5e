<template>
  <Dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    :header="dialogTitle"
    :style="{ width: '80vw', maxWidth: '1000px' }"
    :modal="true"
    :closable="true"
    :draggable="false"
    class="item-form-dialog"
    @hide="handleClose"
  >
    <template #header>
      <div class="flex items-center gap-2">
        <Avatar 
          :icon="dialogIcon" 
          size="small" 
          :class="dialogIconClass" 
        />
        <span class="font-semibold">{{ dialogTitle }}</span>
      </div>
    </template>

    <div class="p-4 max-h-[70vh] overflow-y-auto">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Basic Information Section (Left Column - Narrower) -->
        <div class="lg:col-span-1">
          <Panel header="Basic Information" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-info-circle" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Basic Information</span>
              </div>
            </template>
            <div class="space-y-3">
              <div class="space-y-2">
                <label for="itemType" class="block text-sm font-semibold text-surface-700">Item Type</label>
                <Select
                  id="itemType"
                  v-model="item.type"
                  :options="itemTypes"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Item Type"
                  class="w-full"
                />
              </div>
              
              <div class="space-y-2">
                <label for="itemTitle" class="block text-sm font-semibold text-surface-700">Title</label>
                <InputText
                  id="itemTitle"
                  v-model="item.title"
                  placeholder="Enter item title"
                  class="w-full"
                />
              </div>
            </div>
          </Panel>
        </div>

        <!-- Content Section (Right Column - Wider) -->
        <div class="lg:col-span-2 space-y-3">
          <!-- Question Type Content -->
          <Panel v-if="item.type === 'question'" header="Question Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-question-circle" size="small" class="bg-orange-100 text-orange-600" />
                <span class="font-semibold">Question Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="space-y-2">
                <label class="block text-sm font-semibold text-surface-700">Question Text</label>
                <InputText
                  v-model="item.question"
                  placeholder="Enter your question"
                  class="w-full"
                />
              </div>

              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-semibold text-surface-700">Answer Options</label>
                  <Button
                    label="Add Option"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="addOption"
                    class="transition-all duration-200"
                  />
                </div>
                <div class="space-y-4 max-h-74 overflow-y-auto">
                  <div v-for="(option, index) in item.options" :key="index"
                       class="flex items-center gap-3 rounded-lg hover:border-primary-300 transition-colors">
                    <RadioButton
                      :value="index"
                      v-model="item.correctAnswer"
                      :inputId="`option-${index}`"
                    />
                    <InputText
                      v-model="option.text"
                      placeholder="Enter option text"
                      fluid
                    />
                    <Chip
                      v-if="index === item.correctAnswer"
                      label="Correct"
                      icon="pi pi-check"
                      class="bg-green-100 text-green-700"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      text
                      @click="removeOption(index)"
                      class="transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Panel>

          <!-- Test Type Content -->
          <Panel v-else-if="item.type === 'test'" header="Test Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-list-check" size="small" class="bg-purple-100 text-purple-600" />
                <span class="font-semibold">Test Details</span>
              </div>
            </template>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <label class="block text-sm font-semibold text-surface-700">Test Questions</label>
                <Button
                  label="Add Question"
                  icon="pi pi-plus"
                  size="small"
                  outlined
                  @click="addTestQuestion"
                  class="transition-all duration-200"
                />
              </div>
              
              <div class="space-y-4 max-h-96 overflow-y-auto">
                <div v-for="(testQuestion, qIndex) in item.testQuestions" :key="qIndex"
                     class="border border-surface-300 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-surface-700">Question {{ qIndex + 1 }}</h4>
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      severity="danger"
                      text
                      @click="removeTestQuestion(qIndex)"
                    />
                  </div>
                  
                  <div class="space-y-3">
                    <InputText
                      v-model="testQuestion.question"
                      placeholder="Enter question text"
                      class="w-full"
                    />
                    
                    <div class="space-y-2">
                      <div class="flex items-center justify-between">
                        <label class="text-sm font-medium text-surface-600">Answer Options</label>
                        <Button
                          label="Add Option"
                          icon="pi pi-plus"
                          size="small"
                          text
                          @click="addTestQuestionOption(qIndex)"
                        />
                      </div>
                      
                      <div v-for="(option, oIndex) in testQuestion.options" :key="oIndex"
                           class="flex items-center gap-2">
                        <RadioButton
                          :value="oIndex"
                          v-model="testQuestion.correctAnswer"
                          :inputId="`test-q${qIndex}-option-${oIndex}`"
                        />
                        <InputText
                          v-model="option.text"
                          placeholder="Enter option text"
                          class="flex-1"
                        />
                        <Badge
                          v-if="oIndex === testQuestion.correctAnswer"
                          value="Correct"
                          severity="success"
                          class="ml-2"
                        />
                        <Button
                          icon="pi pi-trash"
                          size="small"
                          severity="danger"
                          text
                          @click="removeTestQuestionOption(qIndex, oIndex)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Panel>

          <!-- Other content types... -->
          <Panel v-else-if="item.type === 'text'" header="Text Content" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-file-text" size="small" class="bg-green-100 text-green-600" />
                <span class="font-semibold">Text Content</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">Content</label>
              <Textarea
                v-model="item.content"
                placeholder="Enter your text content"
                rows="8"
                class="w-full"
              />
            </div>
          </Panel>

          <!-- Link Type Content -->
          <Panel v-else-if="item.type === 'link'" header="Link Details" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar icon="pi pi-link" size="small" class="bg-blue-100 text-blue-600" />
                <span class="font-semibold">Link Details</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">URL</label>
              <InputText
                v-model="item.url"
                placeholder="Enter URL"
                class="w-full"
              />
            </div>
          </Panel>

          <!-- File Upload Types -->
          <Panel v-else-if="['image', 'map', 'diagram'].includes(item.type)" :header="getFileUploadHeader()" class="!rounded-xl">
            <template #header>
              <div class="flex items-center gap-2">
                <Avatar :icon="getFileUploadIcon()" size="small" :class="getFileUploadIconClass()" />
                <span class="font-semibold">{{ getFileUploadHeader() }}</span>
              </div>
            </template>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-surface-700">Upload {{ item.type }}</label>
              <FileUpload
                mode="basic"
                :auto="true"
                choose-label="Choose File"
                class="w-full"
              />
            </div>
          </Panel>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="Cancel"
          icon="pi pi-times"
          outlined
          @click="handleClose"
        />
        <Button
          label="Save Item"
          icon="pi pi-check"
          :disabled="!canSave"
          @click="saveItem"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Item, TestQuestion } from '../../types/item';
import Dialog from 'primevue/dialog';
import Select from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Chip from 'primevue/chip';
import Badge from 'primevue/badge';

interface Props {
  visible: boolean;
  chapterId?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'itemCreated', item: Item): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const store = useMainStore();
const route = useRoute();

const item = ref<Partial<Item>>({
  type: undefined,
  title: '',
  options: [],
  correctAnswer: undefined,
  timedAnswer: '',
  revealTimeSeconds: 30,
  testQuestions: []
});

const itemTypes = [
  { label: 'Question', value: 'question' },
  { label: 'Test', value: 'test' },
  { label: 'Text Content', value: 'text' },
  { label: 'Image Upload', value: 'image' },
  { label: 'Link Details', value: 'link' },
  { label: 'Map Upload', value: 'map' },
  { label: 'Diagram Upload', value: 'diagram' },
  { label: 'Timed Question', value: 'timed-question' }
];

const dialogTitle = computed(() => {
  return 'Create New Item';
});

const dialogIcon = computed(() => {
  return 'pi pi-plus';
});

const dialogIconClass = computed(() => {
  return 'bg-purple-100 text-purple-600';
});

const canSave = computed(() => {
  return item.value.type && item.value.title;
});

// Watch for type changes to initialize appropriate data structures
watch(() => item.value.type, (newType) => {
  if (newType === 'question' && !item.value.options) {
    item.value.options = [];
  }
  if (newType === 'test' && !item.value.testQuestions) {
    item.value.testQuestions = [];
  }
});

// Question methods
const addOption = () => {
  item.value.options = [...(item.value.options || []), { text: '' }];
};

const removeOption = (index: number) => {
  if (item.value.options) {
    item.value.options.splice(index, 1);
    if (item.value.correctAnswer !== undefined && item.value.correctAnswer >= index) {
      if (item.value.correctAnswer === index) {
        item.value.correctAnswer = undefined;
      } else {
        item.value.correctAnswer--;
      }
    }
  }
};

// Test methods
const addTestQuestion = () => {
  if (!item.value.testQuestions) {
    item.value.testQuestions = [];
  }
  const newQuestion: TestQuestion = {
    id: `q-${Date.now()}`,
    question: '',
    options: [{ text: '' }, { text: '' }],
    correctAnswer: 0
  };
  item.value.testQuestions.push(newQuestion);
};

const removeTestQuestion = (index: number) => {
  if (item.value.testQuestions) {
    item.value.testQuestions.splice(index, 1);
  }
};

const addTestQuestionOption = (questionIndex: number) => {
  if (item.value.testQuestions && item.value.testQuestions[questionIndex]) {
    item.value.testQuestions[questionIndex].options.push({ text: '' });
  }
};

const removeTestQuestionOption = (questionIndex: number, optionIndex: number) => {
  if (item.value.testQuestions && item.value.testQuestions[questionIndex]) {
    const question = item.value.testQuestions[questionIndex];
    question.options.splice(optionIndex, 1);
    if (question.correctAnswer >= optionIndex) {
      if (question.correctAnswer === optionIndex) {
        question.correctAnswer = 0;
      } else {
        question.correctAnswer--;
      }
    }
  }
};

// File upload helpers
const getFileUploadHeader = () => {
  const headers = {
    image: 'Image Upload',
    map: 'Map Upload',
    diagram: 'Diagram Upload'
  };
  return headers[item.value.type as keyof typeof headers] || 'File Upload';
};

const getFileUploadIcon = () => {
  const icons = {
    image: 'pi pi-image',
    map: 'pi pi-map',
    diagram: 'pi pi-sitemap'
  };
  return icons[item.value.type as keyof typeof icons] || 'pi pi-file';
};

const getFileUploadIconClass = () => {
  const classes = {
    image: 'bg-pink-100 text-pink-600',
    map: 'bg-teal-100 text-teal-600',
    diagram: 'bg-indigo-100 text-indigo-600'
  };
  return classes[item.value.type as keyof typeof classes] || 'bg-surface-100 text-surface-600';
};

const saveItem = async () => {
  if (props.chapterId && item.value.type && item.value.title && route.params.id) {
    const { id, ...itemData } = { ...item.value } as Item;
    const newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);
    
    // Reset form
    item.value = {
      type: undefined,
      title: '',
      options: [],
      correctAnswer: undefined,
      timedAnswer: '',
      revealTimeSeconds: 30,
      testQuestions: []
    };
    
    emit('itemCreated', newItem);
    emit('update:visible', false);
  }
};

const handleClose = () => {
  // Reset form when closing
  item.value = {
    type: undefined,
    title: '',
    options: [],
    correctAnswer: undefined,
    timedAnswer: '',
    revealTimeSeconds: 30,
    testQuestions: []
  };
  emit('update:visible', false);
};
</script>

<style scoped>
.item-form-dialog :deep(.p-dialog) {
  border-radius: 12px;
}

.item-form-dialog :deep(.p-dialog-header) {
  border-radius: 12px 12px 0 0;
}
</style>
