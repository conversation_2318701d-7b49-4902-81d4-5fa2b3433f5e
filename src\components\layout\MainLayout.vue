<template>
  <div class="main-layout">
    <div class="flex h-full w-full bg-surface-100 px-4 py-10">
      <div class="w-2/5 lg:w-1/4 xl:w-1/5 rounded-r-2xl">
        <Sidebar />
      </div>
      <div class="w-3/5 lg:w-3/4 xl:w-4/5 bg-surface-0 rounded-xl border border-surface-300 mx-6 p-6">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Sidebar from './Sidebar.vue';
</script>

<style scoped>
.main-layout {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>
