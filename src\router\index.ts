import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from '../components/layout/MainLayout.vue';
import AuthLayout from '../components/layout/AuthLayout.vue';
import BookDetails from '../components/views/BookDetails.vue';
import LoginForm from '../components/auth/LoginForm.vue';
import { useAuthStore } from '../stores/auth';

const routes = [
  {
    path: '/auth',
    component: AuthLayout,
    meta: { requiresGuest: true },
    children: [
      {
        path: 'login',
        component: LoginForm,
        meta: { requiresGuest: true }
      }
    ]
  },
  {
    path: '/login',
    redirect: '/auth/login'
  },
  {
    path: '/',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        component: BookDetails,
        meta: { requiresAuth: true }
      },
      {
        path: 'book/:id',
        component: BookDetails,
        meta: { requiresAuth: true }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();

  // Check if user is authenticated
  if (authStore.token && !authStore.user) {
    // Try to restore user session
    await authStore.checkAuth();
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest);

  if (requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login if authentication is required
    next('/auth/login');
  } else if (requiresGuest && authStore.isAuthenticated) {
    // Redirect to home if already authenticated and trying to access guest-only pages
    next('/');
  } else {
    next();
  }
});

export default router;