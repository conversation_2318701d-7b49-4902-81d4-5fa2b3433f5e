import { defineStore } from 'pinia';
import type { User, LoginCredentials, AuthResponse, Permission } from '../types/user';

interface AuthState {
  user: User | null;
  token: string | null;
  permissions: Permission | null;
  loading: boolean;
  error: string | null;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: localStorage.getItem('auth_token'),
    permissions: null,
    loading: false,
    error: null,
  }),

  getters: {
    isAuthenticated: (state) => !!state.token && !!state.user,
    isAdmin: (state) => state.user?.role === 'admin',
    isEditor: (state) => state.user?.role === 'editor' || state.user?.role === 'admin',
    isViewer: (state) => !!state.user,
    
    canView: (state) => state.permissions?.canView ?? false,
    canEdit: (state) => state.permissions?.canEdit ?? false,
    canDelete: (state) => state.permissions?.canDelete ?? false,
    canManageUsers: (state) => state.permissions?.canManageUsers ?? false,
    canCreateBooks: (state) => state.permissions?.canCreateBooks ?? false,
    canDeleteBooks: (state) => state.permissions?.canDeleteBooks ?? false,
    canCreateChapters: (state) => state.permissions?.canCreateChapters ?? false,
    canDeleteChapters: (state) => state.permissions?.canDeleteChapters ?? false,
  },

  actions: {
    async login(credentials: LoginCredentials) {
      this.loading = true;
      this.error = null;

      try {
        const response = await fetch('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(credentials),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Login failed');
        }

        const data: AuthResponse & { permissions: Permission } = await response.json();
        
        this.user = data.user;
        this.token = data.token;
        this.permissions = data.permissions;
        
        // Store token in localStorage
        localStorage.setItem('auth_token', data.token);
        
        return true;
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Login failed';
        return false;
      } finally {
        this.loading = false;
      }
    },

    async logout() {
      this.loading = true;

      try {
        if (this.token) {
          await fetch('http://localhost:3001/api/auth/logout', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.token}`,
            },
          });
        }
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        // Clear state regardless of API call success
        this.user = null;
        this.token = null;
        this.permissions = null;
        this.error = null;
        this.loading = false;
        
        // Remove token from localStorage
        localStorage.removeItem('auth_token');
      }
    },

    async checkAuth() {
      if (!this.token) {
        return false;
      }

      this.loading = true;

      try {
        const response = await fetch('http://localhost:3001/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${this.token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Authentication check failed');
        }

        const data = await response.json();
        this.user = data.user;
        this.permissions = data.permissions;
        
        return true;
      } catch (error) {
        // Clear invalid token
        this.user = null;
        this.token = null;
        this.permissions = null;
        localStorage.removeItem('auth_token');
        
        return false;
      } finally {
        this.loading = false;
      }
    },

    // Helper method to get authorization headers
    getAuthHeaders(): Record<string, string> {
      return this.token ? { 'Authorization': `Bearer ${this.token}` } : {};
    },

    clearError() {
      this.error = null;
    }
  },
});
